# @package database
# Database configuration for MemFuse

# Database type: sqlite or postgres
type: "postgres"

# PostgreSQL configuration
postgres:
  host: ${oc.env:POSTGRES_HOST,localhost}
  port: ${oc.env:POSTGRES_PORT,5432}
  database: ${oc.env:POSTGRES_DB,memfuse}
  user: ${oc.env:POSTGRES_USER,postgres}
  password: ${oc.env:POSTGRES_PASSWORD,postgres}
  # Global connection pool settings (shared across all stores)
  pool_size: 10          # Minimum connections in pool
  max_overflow: 20       # Additional connections beyond pool_size
  pool_timeout: 30.0     # Timeout for getting connection from pool
  pool_recycle: 3600     # Recycle connections after 1 hour
  
# SQLite configuration (fallback)
sqlite:
  path: "data/memfuse.db"

# pgai specific configuration
pgai:
  enabled: true
  vectorizer_worker_enabled: true
  auto_embedding: true
  embedding_model: "all-MiniLM-L6-v2"
  embedding_dimensions: 384
  chunk_size: 1000
  chunk_overlap: 200
  batch_size: 100

  # Immediate trigger configuration
  immediate_trigger: true          # Enable immediate trigger mechanism
  use_polling_fallback: false      # Whether to keep polling as backup

  # Retry mechanism configuration
  max_retries: 3                   # Maximum retry attempts
  retry_interval: 5.0              # Retry interval in seconds
  retry_timeout: 300               # Retry timeout in seconds

  # Worker pool configuration
  worker_count: 3                  # Number of concurrent worker threads
  queue_size: 1000                 # Maximum queue capacity
  batch_processing: false          # Whether to use batch processing

  # Legacy polling configuration (for fallback)
  retry_delay: 5.0                 # Legacy polling interval

  # Monitoring configuration
  enable_metrics: true             # Enable performance monitoring
  log_level: "INFO"               # Log level for embedding operations
