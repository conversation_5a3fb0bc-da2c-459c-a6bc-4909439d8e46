# PostgreSQL Connection Pool Optimization

## Overview

This document describes the comprehensive PostgreSQL connection pool optimization implemented to resolve connection leak issues in MemFuse. The solution follows the singleton design pattern and provides centralized connection management across all services and users.

## Problem Statement

### Original Issue
Multiple calls to the query API (`/users/{user_id}/query`) resulted in PostgreSQL connection errors:

```
[2025-07-29 16:27:07,650][psycopg.pool][WARNING] - error connecting in 'pool-5': 
connection failed: connection to server at "127.0.0.1", port 5432 failed: 
FATAL: sorry, too many clients already
```

### Root Causes
1. **Multiple Individual Connection Pools**: Each PgaiStore instance created its own connection pool (1-2 connections each)
2. **No Pool Sharing**: Multiple users and memory layers (M0, M1, M2) created separate store instances
3. **Poor Connection Cleanup**: Destructor-based cleanup was unreliable in async contexts
4. **Configuration Ignored**: Hardcoded pool sizes ignored configuration files
5. **Connection Accumulation**: Connections weren't properly released, accumulating until PostgreSQL limit

### Impact
- API became unusable after several query requests
- Required service restart to recover
- Poor resource utilization and scalability

## Solution Architecture

### Design Principles
Following the [MemFuse Singleton Optimization Strategy](singleton.md):

1. **Tier 1 Global Singleton**: One connection pool per database URL
2. **Configuration-Driven**: Pool settings read from configuration hierarchy
3. **Resource Efficiency**: Shared pools across all users and services
4. **Proper Lifecycle Management**: Explicit cleanup and monitoring
5. **Backward Compatibility**: Same interfaces for existing code

### Core Components

#### 1. GlobalConnectionManager (Tier 1 Singleton)
```python
class GlobalConnectionManager:
    """
    Tier 1 Global Singleton: PostgreSQL Connection Pool Manager
    
    Features:
    - Single connection pool per database URL
    - Configuration-driven pool sizing
    - Automatic cleanup and lifecycle management
    - Connection monitoring and statistics
    - Thread-safe operations
    """
```

**Key Methods:**
- `get_connection_pool()`: Get or create shared connection pool
- `close_all_pools()`: Cleanup all pools during shutdown
- `get_pool_statistics()`: Monitor pool usage and health

#### 2. Configuration Hierarchy
Configuration priority (highest to lowest):
1. `store.database.postgres.*`
2. `database.postgres.*` 
3. `postgres.*`
4. Default values

**Configuration Example:**
```yaml
# config/database/default.yaml
postgres:
  pool_size: 10          # Minimum connections in pool
  max_overflow: 20       # Additional connections beyond pool_size
  pool_timeout: 30.0     # Timeout for getting connection from pool
  pool_recycle: 3600     # Recycle connections after 1 hour
```

#### 3. Store Integration
PgaiStore instances now use the global connection manager:

```python
# Get shared connection pool with store reference for tracking
self.pool = await connection_manager.get_connection_pool(
    db_url=self.db_url,
    config=self.db_config,
    store_ref=self  # Pass self for reference tracking
)
```

## Implementation Details

### Connection Pool Sharing
- **One pool per database URL**: Multiple stores with same database share one pool
- **Reference tracking**: Weak references track which stores use each pool
- **Automatic cleanup**: Pools closed when no active references remain

### Configuration Management
```python
@classmethod
def from_memfuse_config(cls, config: Dict[str, Any]) -> 'ConnectionPoolConfig':
    """Create configuration from MemFuse config hierarchy."""
    postgres_config = {}
    
    # Layer 1: Base postgres config
    if "postgres" in config:
        postgres_config.update(config["postgres"])
    
    # Layer 2: Database postgres config (higher priority)
    if "database" in config and "postgres" in config["database"]:
        postgres_config.update(config["database"]["postgres"])
    
    # Layer 3: Store database postgres config (highest priority)
    if ("store" in config and 
        "database" in config["store"] and 
        "postgres" in config["store"]["database"]):
        postgres_config.update(config["store"]["database"]["postgres"])
```

### Lifecycle Management
1. **Initialization**: Pools created on first use with configuration
2. **Reference Tracking**: Weak references automatically cleaned up
3. **Graceful Shutdown**: All pools closed during service shutdown
4. **Health Monitoring**: Pool statistics available for monitoring

## Performance Improvements

### Before Optimization
- **Per user**: 1-3 store instances × 2 connections = 2-6 connections
- **Multiple users**: 5 users × 6 connections = 30 connections
- **Connection accumulation**: Connections not released properly
- **Resource waste**: Many idle individual pools

### After Optimization
- **Global sharing**: 1 connection pool for all stores
- **Configurable sizing**: 10-30 connections total (configurable)
- **Proper cleanup**: Automatic reference counting and cleanup
- **Resource efficiency**: Shared pools across all users

### Test Results
From integration tests:
- ✅ **5 store instances share 1 connection pool**
- ✅ **Connection increase: only 3 connections for 5 stores**
- ✅ **No connection leaks**: 10 API requests with stable connection count
- ✅ **Configuration applied**: Pool settings from config files used correctly

## Configuration Options

### Database Configuration
```yaml
# config/database/default.yaml
postgres:
  host: ${oc.env:POSTGRES_HOST,localhost}
  port: ${oc.env:POSTGRES_PORT,5432}
  database: ${oc.env:POSTGRES_DB,memfuse}
  user: ${oc.env:POSTGRES_USER,postgres}
  password: ${oc.env:POSTGRES_PASSWORD,postgres}
  
  # Global connection pool settings
  pool_size: 10          # Minimum connections in pool
  max_overflow: 20       # Additional connections beyond pool_size
  pool_timeout: 30.0     # Timeout for getting connection from pool
  pool_recycle: 3600     # Recycle connections after 1 hour
```

### Store-Specific Configuration
```yaml
# config/store/pgai.yaml
database:
  postgres:
    pool_size: 15          # Override for pgai stores
    max_overflow: 25       # Higher limits for vector operations
    pool_timeout: 45.0     # Longer timeout for complex queries
```

### Environment Variables
All configuration values support environment variable overrides:
- `POSTGRES_HOST`, `POSTGRES_PORT`, `POSTGRES_DB`
- `POSTGRES_USER`, `POSTGRES_PASSWORD`

## Monitoring and Diagnostics

### Pool Statistics
```python
# Get comprehensive pool statistics
stats = connection_manager.get_pool_statistics()
# Returns:
{
    "postgresql://postgres:***@localhost:5432/memfuse": {
        "min_size": 10,
        "max_size": 30,
        "timeout": 30.0,
        "recycle": 3600,
        "active_references": 5,
        "pool_closed": False
    }
}
```

### Connection Monitoring
Use the provided monitoring tools:

```bash
# Monitor current connections
poetry run python tests/connection_monitor.py

# Continuous monitoring
poetry run python tests/connection_monitor.py continuous 10 60

# Test connection leak prevention
poetry run python tests/integration/connection_pool/test_connection_leak_prevention.py
```

## Testing

### Test Suite
Located in `tests/integration/connection_pool/`:

1. **test_global_connection_manager.py**: Singleton pattern and pool sharing
2. **test_connection_pool_configuration.py**: Configuration hierarchy and application
3. **test_connection_leak_prevention.py**: Connection leak prevention under load

### Running Tests
```bash
# Run all connection pool tests
poetry run python scripts/run_tests.py tests/integration/connection_pool/test_global_connection_manager.py -v

# Run specific test
poetry run python tests/integration/connection_pool/test_connection_leak_prevention.py

# Monitor connections during tests
poetry run python tests/connection_monitor.py continuous 5 120 &
poetry run python tests/simple_api_test.py
```

## Best Practices

### For Developers
1. **Use Global Manager**: Always use `get_global_connection_manager()` for pools
2. **Proper Cleanup**: Call `await store.close()` when done with stores
3. **Configuration**: Use configuration files instead of hardcoded values
4. **Monitoring**: Monitor pool statistics in production

### For Operations
1. **Pool Sizing**: Start with 10-30 connections, adjust based on load
2. **Monitoring**: Watch for connection count growth over time
3. **Alerts**: Set up alerts for high connection usage
4. **Graceful Shutdown**: Ensure proper service shutdown to close pools

### For Testing
1. **Cleanup**: Always cleanup pools in test teardown
2. **Isolation**: Use separate database URLs for test isolation
3. **Monitoring**: Include connection monitoring in integration tests

## Migration Guide

### Existing Code
No changes required for existing PgaiStore usage:
```python
# This continues to work unchanged
store = PgaiStore(config=config, table_name="my_table")
await store.initialize()
# Now uses shared connection pool automatically
```

### Service Shutdown
Add proper cleanup to service shutdown:
```python
# In service shutdown
from memfuse_core.services.service_factory import ServiceFactory
await ServiceFactory.cleanup_all_services()
```

## Future Enhancements

1. **Connection Pool Metrics**: Prometheus metrics for pool usage
2. **Dynamic Pool Sizing**: Automatic scaling based on load
3. **Connection Health Checks**: Periodic connection validation
4. **Multi-Database Support**: Pool management for multiple databases
5. **Connection Routing**: Read/write connection separation

## Conclusion

The PostgreSQL connection pool optimization successfully resolves the connection leak issue while improving resource efficiency and scalability. The solution follows MemFuse's singleton design principles and provides a robust foundation for future database operations.

**Key Benefits:**
- ✅ **Eliminated connection leaks**
- ✅ **Improved resource efficiency** 
- ✅ **Configuration-driven management**
- ✅ **Better monitoring and diagnostics**
- ✅ **Maintained backward compatibility**
